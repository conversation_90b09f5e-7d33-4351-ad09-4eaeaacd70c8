# 自动编辑模式 (Auto Edit Mode)

## 概述

自动编辑模式允许您在不修改笔记类型模板的情况下使字段可编辑。这是对传统 `{{edit:Field}}` 方法的补充，提供了更灵活的配置选项。

## 功能特点

- **无需修改模板**：不需要在笔记类型模板中添加 `{{edit:}}` 前缀
- **灵活的字段过滤**：可以指定包含或排除特定字段
- **笔记类型限制**：可以限制只对特定笔记类型生效
- **向后兼容**：与现有的 `{{edit:Field}}` 方法完全兼容

## 配置方法

1. 打开插件配置窗口
2. 转到 "自动编辑" 标签页
3. 勾选 "启用自动编辑模式"
4. 根据需要配置过滤选项

### 配置选项说明

#### 字段过滤

- **排除字段**：
  - 输入不希望可编辑的字段名，用逗号分隔
  - 例如：`Tags, Extra, Source`
  - 这些字段将不会自动变为可编辑

- **仅包含字段**：
  - 输入希望可编辑的字段名，用逗号分隔
  - 例如：`Front, Back`
  - 如果指定了此选项，只有列出的字段会变为可编辑
  - 留空表示所有字段都可编辑（除了排除的字段）

#### 笔记类型过滤

- **限制笔记类型**：
  - 输入希望启用自动编辑的笔记类型名称，用逗号分隔
  - 例如：`基础, 填空, 图像遮挡`
  - 留空表示对所有笔记类型生效

## 使用示例

### 示例1：基本使用
- 启用自动编辑模式
- 其他选项留空
- 结果：所有字段在所有笔记类型中都可编辑

### 示例2：排除特定字段
- 启用自动编辑模式
- 排除字段：`Tags, Source`
- 结果：除了 Tags 和 Source 字段外，其他字段都可编辑

### 示例3：只编辑特定字段
- 启用自动编辑模式
- 仅包含字段：`Front, Back`
- 结果：只有 Front 和 Back 字段可编辑

### 示例4：限制笔记类型
- 启用自动编辑模式
- 限制笔记类型：`Basic, Cloze`
- 结果：只有 Basic 和 Cloze 类型的笔记中的字段可编辑

## 优先级规则

1. **显式 `{{edit:}}` 标记**：具有最高优先级，始终可编辑
2. **排除字段**：被排除的字段不可编辑（除非有显式 `{{edit:}}` 标记）
3. **仅包含字段**：如果指定了此选项，只有列出的字段可编辑
4. **笔记类型限制**：只对指定的笔记类型生效

## 注意事项

- 配置更改需要重新开始复习会话才能生效
- 自动编辑模式与传统的 `{{edit:Field}}` 方法完全兼容
- 如果同时使用两种方法，显式的 `{{edit:}}` 标记具有更高优先级
- 字段名称区分大小写，请确保输入正确的字段名称

## 故障排除

如果自动编辑模式不工作：

1. 检查是否已启用自动编辑模式
2. 确认字段名称拼写正确（区分大小写）
3. 检查笔记类型名称是否正确
4. 重新开始复习会话
5. 查看 Anki 的调试控制台是否有错误信息

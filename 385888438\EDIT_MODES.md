# 编辑模式功能说明

## 概述

插件现在支持多种编辑模式，让您可以更灵活地编辑字段内容：

1. **传统 Ctrl+点击编辑**
2. **双击编辑**（新增）
3. **划词编辑**（新增）
4. **自动编辑模式**

## 编辑模式详解

### 1. Ctrl+点击编辑（传统模式）

- **启用方式**：勾选"Ctrl + 点击编辑字段"
- **使用方法**：按住 Ctrl 键（Mac 上为 Cmd）并点击字段
- **特点**：需要按键配合，精确控制

### 2. 双击编辑（新增功能）

- **启用方式**：勾选"双击字段进行编辑"
- **使用方法**：直接双击字段内容
- **特点**：
  - 无需按住任何键
  - 快速进入编辑模式
  - 阻止默认的文本选择行为

### 3. 划词编辑（新增功能）

- **启用方式**：勾选"划词编辑（选中文本后自动进入编辑模式）"
- **使用方法**：用鼠标选中字段中的任意文本
- **特点**：
  - 选中文本后自动进入编辑模式
  - 适合快速修改部分内容
  - 保持文本选择状态

### 4. 自动编辑模式

- **启用方式**：在"自动编辑"标签页中启用
- **使用方法**：字段自动可编辑，直接点击即可
- **特点**：无需任何额外操作

## 配置组合建议

### 推荐配置1：精确控制
- ✅ Ctrl+点击编辑
- ❌ 双击编辑
- ❌ 划词编辑
- ❌ 自动编辑模式

**适合**：需要精确控制何时编辑的用户

### 推荐配置2：便捷编辑
- ❌ Ctrl+点击编辑
- ✅ 双击编辑
- ✅ 划词编辑
- ❌ 自动编辑模式

**适合**：希望快速编辑但不希望误触的用户

### 推荐配置3：最大便利
- ❌ Ctrl+点击编辑
- ✅ 双击编辑
- ✅ 划词编辑
- ✅ 自动编辑模式（仅特定字段）

**适合**：经常需要编辑字段的用户

## 视觉反馈

### 悬停效果
- 当启用双击或划词编辑时，鼠标悬停在字段上会显示淡蓝色背景
- 光标会变为文本选择样式

### 选择效果
- 选中文本时会显示蓝色高亮背景
- 兼容 Firefox 和其他浏览器

### 编辑状态
- 进入编辑模式后，字段会显示蓝色轮廓（如果启用了轮廓选项）
- 显示字段名称占位符（在 Ctrl+点击模式下）

## 使用技巧

1. **组合使用**：可以同时启用多种编辑模式，系统会智能处理
2. **快速修改**：使用划词编辑可以快速选中并修改特定文本
3. **批量编辑**：结合自动编辑模式，可以快速在多个字段间切换
4. **避免误触**：如果经常误触，建议只启用 Ctrl+点击模式

## 兼容性

- ✅ 与现有的 {{edit:}} 模板标记完全兼容
- ✅ 支持所有现有的格式化功能
- ✅ 支持图片缩放功能
- ✅ 支持快捷键功能
- ✅ 兼容其他插件（如弹出词典）

## 故障排除

如果编辑功能不工作：

1. 检查是否启用了相应的编辑模式选项
2. 确认字段已正确配置为可编辑
3. 重新开始复习会话
4. 检查是否与其他插件冲突

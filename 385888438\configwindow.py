import re
from enum import Enum
from typing import List, TypedDict, Set, TYPE_CHECKING

from anki.models import NoteType
from aqt import mw
from aqt.qt import *

from .ankiaddonconfig import ConfigManager, ConfigWindow

conf = ConfigManager()


def general_tab(conf_window: ConfigWindow) -> None:
    tab = conf_window.add_tab("常规")
    tab.checkbox(
        "ctrl_click",
        "Ctrl + 点击编辑字段（Mac 上为 Cmd）",
        tooltip="如果不勾选，则无需按住 Ctrl 键",
    )
    tab.checkbox("outline", "编辑时在字段周围显示蓝色轮廓")
    tab.checkbox("process_paste", "处理粘贴的图片和 HTML 内容")
    tab.checkbox("disable_autoplay_after_edit", "编辑后禁用自动播放")
    tag_options = ["div", "span"]
    tab.dropdown(
        "tag",
        tag_options,
        tag_options,
        "可编辑字段使用的 HTML 标签：",
        tooltip="推荐使用 div",
    )
    tab.text_input(
        "shortcuts.cloze-alt",
        "相同编号填空的快捷键：",
        tooltip="默认为 Ctrl+Shift+Alt+C",
    )

    tab.space(20)
    tab.text("图片缩放", bold=True)
    tab.checkbox(
        "resize_image_default_state",
        "使用图片缩放功能",
        tooltip="即使不勾选，也可以使用下面的快捷键切换图片缩放模式",
    )
    option_labels = [
        "不保持比例",
        "使用角落时保持比例",
        "始终保持比例",
    ]
    option_values = [0, 1, 2]
    tab.dropdown(
        "resize_image_preserve_ratio",
        option_labels,
        option_values,
        "图片缩放模式：",
    )
    tab.text_input(
        "shortcuts.image-resize",
        "图片缩放模式快捷键：",
        tooltip="按此快捷键可切换图片缩放模式",
    )
    tab.stretch()


def formatting_tab(conf_window: ConfigWindow) -> None:
    conf = conf_window.conf
    tab = conf_window.add_tab("格式化")
    tab.setContentsMargins(25, 25, 25, 25)

    # 格式化选项的中文翻译
    formatting_translations = {
        "fontcolor": "字体颜色",
        "formatblock": "格式块",
        "highlight": "高亮",
        "hyperlink": "超链接",
        "indent": "缩进",
        "justifyCenter": "居中对齐",
        "justifyFull": "两端对齐",
        "justifyLeft": "左对齐",
        "justifyRight": "右对齐",
        "orderedlist": "有序列表",
        "outdent": "减少缩进",
        "removeformat": "清除格式",
        "strikethrough": "删除线",
        "subscript": "下标",
        "superscript": "上标",
        "unhyperlink": "取消超链接",
        "unorderedlist": "无序列表"
    }

    scroll_layout = tab.scroll_layout(horizontal=False)
    for formatting in conf["special_formatting"]:
        hlayout = scroll_layout.hlayout()
        item_key = f"special_formatting.{formatting}"
        hlayout.checkbox(f"{item_key}.enabled")

        # 使用中文翻译，如果没有翻译则使用原英文
        display_name = formatting_translations.get(formatting, formatting)
        hlayout.text(display_name).setFixedWidth(120)

        hlayout.text_input(f"{item_key}.shortcut").setFixedWidth(160)
        if conf[f"{item_key}.arg"] is not None:
            if conf[f"{item_key}.arg.type"] == "color":
                hlayout.color_input(f"{item_key}.arg.value")
            else:
                hlayout.text_input(f"{item_key}.arg.value").setFixedWidth(60)
        hlayout.stretch()
    tab.stretch()


class TemplateField(TypedDict):
    name: str
    edit: bool


class Editability(Enum):
    NONE = 0
    PARTIAL = 1
    ALL = 2

    @classmethod
    def from_check_state(cls, check_state: Qt.CheckState) -> "Editability":
        if check_state == Qt.CheckState.Unchecked:
            return cls.NONE
        if check_state == Qt.CheckState.Checked:
            return cls.ALL
        return cls.PARTIAL

    @classmethod
    def to_check_state(cls, val: "Editability") -> Qt.CheckState:
        if val == cls.NONE:
            return Qt.CheckState.Unchecked
        if val == cls.ALL:
            return Qt.CheckState.Checked
        return Qt.CheckState.PartiallyChecked


class FieldIsEditable(TypedDict):
    name: str
    orig_edit: Editability
    edit: Editability


class NoteTypeFields(TypedDict):
    name: str
    fields: List[FieldIsEditable]


def modify_field_editability(
    note_type: "NoteType", field: FieldIsEditable
) -> "NoteType":
    for template in note_type["tmpls"]:
        for side in ["qfmt", "afmt"]:
            if field["edit"] == Editability.ALL:
                template[side] = re.sub(
                    "{{((?:(?!edit:)[^#/:}]+:)*%s)}}" % re.escape(field["name"]),
                    r"{{edit:\1}}",
                    template[side],
                )
            elif field["edit"] == Editability.NONE:
                template[side] = re.sub(
                    "{{((?:[^#/:}]+:)*)edit:((?:[^#/:}]+:)*%s)}}" % re.escape(field["name"]),
                    r"{{\1\2}}",
                    template[side],
                )
    return note_type


def parse_fields(template: str) -> List[TemplateField]:
    matches = re.findall("{{[^#/}]+?}}", template)  # type: ignore
    fields = []
    for m in matches:
        # strip off mustache
        m = re.sub(r"[{}]", "", m)
        # strip off modifiers
        splitted = m.split(":")
        modifiers = splitted[:-1]
        field_name = splitted[-1]
        has_edit = "edit" in modifiers
        field_info = TemplateField(name=field_name, edit=has_edit)
        fields.append(field_info)
    return fields


def get_fields_in_every_notetype(fields_in_note_type: List[NoteTypeFields]) -> None:
    for i in fields_in_note_type:
        fields_in_note_type.pop()

    models = mw.col.models
    note_types = models.all()
    for note_type in note_types:
        templates = note_type["tmpls"]
        editable_field_names: Set[str] = set()
        uneditable_field_names: Set[str] = set()

        for template in templates:
            for side in ["qfmt", "afmt"]:
                for tmpl_field in parse_fields(template[side]):  # type: ignore
                    name = tmpl_field["name"]
                    if tmpl_field["edit"]:
                        editable_field_names.update([name])
                    else:
                        uneditable_field_names.update([name])

        field_names = [fld["name"] for fld in note_type["flds"]]
        fields_list = []
        for fldname in field_names:
            try:
                # if (False, False), skip since the field isn't used in any of the templates.
                editable = {
                    (True, True): Editability.PARTIAL,
                    (True, False): Editability.ALL,
                    (False, True): Editability.NONE,
                }[(fldname in editable_field_names, fldname in uneditable_field_names)]

                field = FieldIsEditable(name=fldname, edit=editable, orig_edit=editable)
                fields_list.append(field)
            except:
                pass
        nt = NoteTypeFields(name=note_type["name"], fields=fields_list)
        fields_in_note_type.append(nt)


def fields_tab(conf_window: ConfigWindow) -> None:
    tab = conf_window.add_tab("字段")
    dropdown = QComboBox()
    tab.addWidget(dropdown)
    tab.space(10)
    tab.text("勾选复选框以使字段在复习时可编辑")
    qlist = QListWidget()
    qlist.setStyleSheet("QListWidget{border: 1px solid; padding: 6px;}")
    tab.addWidget(qlist)

    fields_in_note_type: List[NoteTypeFields] = []

    def update_label_status(idx: int) -> None:
        notetype = fields_in_note_type[idx]
        editable = {Editability.NONE: 0, Editability.PARTIAL: 0, Editability.ALL: 0}
        for field in notetype["fields"]:
            editable[field["edit"]] += 1
        if editable[Editability.ALL] + editable[Editability.PARTIAL] == 0:
            status = "❌"
        elif editable[Editability.NONE] + editable[Editability.PARTIAL] == 0:
            status = "✅"
        else:
            status = "🔶"
        old_label = dropdown.itemText(idx)
        new_label = old_label[:-1] + status
        dropdown.setItemText(idx, new_label)

    def on_check(item: QListWidgetItem) -> None:
        nt_idx = dropdown.currentIndex()
        fields = fields_in_note_type[nt_idx]["fields"]
        field = fields[qlist.row(item)]
        field["edit"] = Editability.from_check_state(item.checkState())
        update_label_status(nt_idx)

    def on_double_click(item: QListWidgetItem) -> None:
        curr_check = item.checkState()
        if curr_check == Qt.CheckState.Checked:
            item.setCheckState(Qt.CheckState.Unchecked)
        else:  # Partially checked or unchecked
            item.setCheckState(Qt.CheckState.Checked)

    def switch_template(idx: int) -> None:
        if idx == -1:
            return
        qlist.clear()
        fields = fields_in_note_type[idx]["fields"]
        for field in fields:
            item = QListWidgetItem(field["name"], qlist, QListWidgetItem.ItemType.Type)
            qlist.addItem(item)
            item.setCheckState(Editability.to_check_state(field["edit"]))

    def on_save() -> None:
        for note_type_fields in fields_in_note_type:
            modified = False
            try:  # 2.1.45
                note_type = mw.col.models.by_name(note_type_fields["name"])
            except:  # 2.1.41-44
                note_type = mw.col.models.byName(  # type: ignore
                    note_type_fields["name"]
                )
            for field in note_type_fields["fields"]:
                if field["edit"] != field["orig_edit"]:
                    modified = True
                    note_type = modify_field_editability(note_type, field)
            if modified:
                mw.col.models.save(note_type)

    qlist.itemChanged.connect(on_check)
    qlist.itemDoubleClicked.connect(on_double_click)
    dropdown.currentIndexChanged.connect(switch_template)
    conf_window.execute_on_save(on_save)

    get_fields_in_every_notetype(fields_in_note_type)
    for idx, nt in enumerate(fields_in_note_type):
        dropdown.addItem(nt["name"] + "  ")
        update_label_status(idx)
    dropdown.setCurrentIndex(0)  # Triggers currentIndexChanged

    def make_every_field_editable() -> None:
        for idx, note_type_fields in enumerate(fields_in_note_type):
            for field in note_type_fields["fields"]:
                field["edit"] = Editability.ALL
            update_label_status(idx)
        switch_template(dropdown.currentIndex())

    tab.space(10)
    button_layout = tab.hlayout()
    button_layout.stretch()
    button = QPushButton("使所有笔记类型的所有字段可编辑 ✅")
    button.clicked.connect(make_every_field_editable)
    button_layout.addWidget(button)
    button_layout.stretch()
    tab.space(5)


def auto_edit_tab(conf_window: ConfigWindow) -> None:
    """自动编辑模式配置标签页"""
    tab = conf_window.add_tab("自动编辑")
    tab.setContentsMargins(25, 25, 25, 25)

    # 启用自动编辑模式
    tab.checkbox(
        "auto_edit_mode.enabled",
        "启用自动编辑模式",
        tooltip="自动使所有字段可编辑，无需在模板中添加 {{edit:}} 标记"
    )

    tab.space(15)
    tab.text("字段过滤", bold=True)

    # 排除字段
    tab.text("排除字段（用逗号分隔）：")
    tab.text_input(
        "auto_edit_mode.exclude_fields_text",
        "",
        tooltip="这里列出的字段将不会自动变为可编辑。例如：Tags,Extra"
    )

    tab.space(10)

    # 仅包含特定字段
    tab.text("仅包含字段（用逗号分隔，留空表示包含所有字段）：")
    tab.text_input(
        "auto_edit_mode.include_only_fields_text",
        "",
        tooltip="如果指定了此选项，只有这些字段会变为可编辑。例如：Front,Back"
    )

    tab.space(15)
    tab.text("笔记类型过滤", bold=True)

    # 笔记类型
    tab.text("限制笔记类型（用逗号分隔，留空表示所有类型）：")
    tab.text_input(
        "auto_edit_mode.note_types_text",
        "",
        tooltip="如果指定了此选项，自动编辑只对这些笔记类型生效。例如：基础,填空"
    )

    tab.space(15)
    tab.text(
        "注意：自动编辑模式与传统的 {{edit:}} 方法并行工作。"
        "带有 {{edit:}} 标记的字段无论如何都会保持可编辑状态。",
        multiline=True
    )

    tab.stretch()


def about_tab(conf_window: ConfigWindow) -> None:
    conf = conf_window.conf
    tab = conf_window.add_tab("关于")
    tab.text("复习时编辑字段（填空）", bold=True, size=20)
    tab.text("© 2019-2021 Yoonchae Lee (Bluegreenmagick)")
    tab.text(f"版本 {conf['version.major']}.{conf['version.minor']}")
    tab.text(
        "发现了错误？"
        " <a href='https://github.com/BlueGreenMagick/Edit-Field-During-Review-Cloze/issues'>"
        "在这里报告问题"
        "</a>。",
        html=True,
    )
    tab.space(15)
    tab.text("许可证", bold=True)
    tab.text(
        "复习时编辑字段（填空）是一个自由开源软件（FOSS），"
        "基于 GNU AGPL v3 许可证分发。"
        "它也可能包含基于不同许可证的代码。"
        "请查看 LICENSE 文件了解更多信息。",
        multiline=True,
    )
    tab.stretch()


def process_auto_edit_config(config_manager: ConfigManager) -> None:
    """Process auto-edit configuration, converting text inputs to lists."""

    # Convert text inputs to lists for exclude_fields
    exclude_text = config_manager.get("auto_edit_mode.exclude_fields_text", "")
    if exclude_text:
        exclude_list = [field.strip() for field in exclude_text.split(",") if field.strip()]
        config_manager.set("auto_edit_mode.exclude_fields", exclude_list)
    else:
        config_manager.set("auto_edit_mode.exclude_fields", [])

    # Convert text inputs to lists for include_only_fields
    include_text = config_manager.get("auto_edit_mode.include_only_fields_text", "")
    if include_text:
        include_list = [field.strip() for field in include_text.split(",") if field.strip()]
        config_manager.set("auto_edit_mode.include_only_fields", include_list)
    else:
        config_manager.set("auto_edit_mode.include_only_fields", [])

    # Convert text inputs to lists for note_types
    note_types_text = config_manager.get("auto_edit_mode.note_types_text", "")
    if note_types_text:
        note_types_list = [nt.strip() for nt in note_types_text.split(",") if nt.strip()]
        config_manager.set("auto_edit_mode.note_types", note_types_list)
    else:
        config_manager.set("auto_edit_mode.note_types", [])


def load_auto_edit_config(conf_window: ConfigWindow) -> None:
    """Load auto-edit configuration, converting lists to text inputs."""
    conf = conf_window.conf

    # Convert lists to text for display
    exclude_fields = conf.get("auto_edit_mode.exclude_fields", [])
    conf["auto_edit_mode.exclude_fields_text"] = ", ".join(exclude_fields)

    include_fields = conf.get("auto_edit_mode.include_only_fields", [])
    conf["auto_edit_mode.include_only_fields_text"] = ", ".join(include_fields)

    note_types = conf.get("auto_edit_mode.note_types", [])
    conf["auto_edit_mode.note_types_text"] = ", ".join(note_types)


def with_window(conf_window: ConfigWindow) -> None:
    conf_window.set_footer("更改将在您开始复习会话时生效")

    # Load auto-edit config when window opens
    load_auto_edit_config(conf_window)

    # Process auto-edit config when saving
    def save_hook():
        process_auto_edit_config(conf_window.conf)

    conf_window.execute_on_save(save_hook)


conf.use_custom_window()
conf.on_window_open(with_window)
conf.add_config_tab(general_tab)
conf.add_config_tab(formatting_tab)
conf.add_config_tab(fields_tab)
conf.add_config_tab(auto_edit_tab)
conf.add_config_tab(about_tab)

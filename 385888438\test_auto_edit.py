#!/usr/bin/env python3
"""
测试自动编辑模式功能的简单脚本
"""

import base64
from unittest.mock import Mock, MagicMock

# 模拟 Anki 相关模块
class MockCard:
    def __init__(self, note_type_name="Basic"):
        self._note_type = {"name": note_type_name}
    
    def note_type(self):
        return self._note_type

class MockTemplateRenderContext:
    def __init__(self, card=None):
        self._card = card or MockCard()
    
    def card(self):
        return self._card

# 模拟配置
class MockConfig:
    def __init__(self):
        self.config = {
            "auto_edit_mode": {
                "enabled": True,
                "exclude_fields": [],
                "include_only_fields": [],
                "note_types": []
            },
            "outline": True,
            "ctrl_click": False,
            "tag": "div"
        }
    
    def get(self, key, default=None):
        keys = key.split('.')
        value = self.config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value
    
    def __getitem__(self, key):
        return self.get(key)

# 导入要测试的函数（需要先设置模拟的配置）
conf = MockConfig()

def should_auto_edit_field(field: str, ctx) -> bool:
    """Check if a field should be automatically made editable based on configuration."""
    auto_config = conf.get("auto_edit_mode", {})
    
    if not auto_config.get("enabled", False):
        return False
    
    # Check note type restrictions
    note_types = auto_config.get("note_types", [])
    if note_types and ctx.card() and ctx.card().note_type()["name"] not in note_types:
        return False
    
    # Check field exclusions
    exclude_fields = auto_config.get("exclude_fields", [])
    if field in exclude_fields:
        return False
    
    # Check field inclusions (if specified, only these fields are editable)
    include_only = auto_config.get("include_only_fields", [])
    if include_only and field not in include_only:
        return False
    
    return True

def wrap_field_for_editing(txt: str, field: str) -> str:
    """Wrap field content with editing attributes."""
    class_name = ""
    if conf["outline"]:
        class_name += "EFDRC-outline "
    if conf["ctrl_click"]:
        class_name += "EFDRC-ctrl "
    field_encoded = base64.b64encode(field.encode("utf-8")).decode("ascii")
    return """<%s data-EFDRCfield="%s" class="%s">%s</%s>""" % (
        conf["tag"],
        field_encoded,
        class_name,
        txt,
        conf["tag"],
    )

def edit_filter(txt: str, field: str, filt: str, ctx) -> str:
    # Check for explicit {{edit:}} filter
    if filt == "edit":
        return wrap_field_for_editing(txt, field)
    
    # Check for auto-edit mode
    if should_auto_edit_field(field, ctx):
        return wrap_field_for_editing(txt, field)
    
    return txt

def test_auto_edit_functionality():
    """测试自动编辑功能"""
    print("开始测试自动编辑模式...")
    
    # 测试1: 基本自动编辑
    ctx = MockTemplateRenderContext()
    result = edit_filter("Hello World", "Front", "", ctx)
    expected_field = base64.b64encode("Front".encode("utf-8")).decode("ascii")
    expected = f'<div data-EFDRCfield="{expected_field}" class="EFDRC-outline ">Hello World</div>'
    assert result == expected, f"测试1失败: {result} != {expected}"
    print("✓ 测试1通过: 基本自动编辑")
    
    # 测试2: 显式edit过滤器
    result = edit_filter("Hello World", "Front", "edit", ctx)
    assert result == expected, f"测试2失败: {result} != {expected}"
    print("✓ 测试2通过: 显式edit过滤器")
    
    # 测试3: 排除字段
    conf.config["auto_edit_mode"]["exclude_fields"] = ["Back"]
    result = edit_filter("Hello World", "Back", "", ctx)
    assert result == "Hello World", f"测试3失败: {result} != 'Hello World'"
    print("✓ 测试3通过: 排除字段")
    
    # 测试4: 仅包含特定字段
    conf.config["auto_edit_mode"]["exclude_fields"] = []
    conf.config["auto_edit_mode"]["include_only_fields"] = ["Front"]
    result = edit_filter("Hello World", "Back", "", ctx)
    assert result == "Hello World", f"测试4失败: {result} != 'Hello World'"
    result = edit_filter("Hello World", "Front", "", ctx)
    assert result == expected, f"测试4失败: Front字段应该可编辑"
    print("✓ 测试4通过: 仅包含特定字段")
    
    # 测试5: 笔记类型限制
    conf.config["auto_edit_mode"]["include_only_fields"] = []
    conf.config["auto_edit_mode"]["note_types"] = ["Cloze"]
    ctx_basic = MockTemplateRenderContext(MockCard("Basic"))
    ctx_cloze = MockTemplateRenderContext(MockCard("Cloze"))
    
    result_basic = edit_filter("Hello World", "Front", "", ctx_basic)
    assert result_basic == "Hello World", f"测试5失败: Basic类型不应该可编辑"
    
    result_cloze = edit_filter("Hello World", "Front", "", ctx_cloze)
    expected_cloze = f'<div data-EFDRCfield="{expected_field}" class="EFDRC-outline ">Hello World</div>'
    assert result_cloze == expected_cloze, f"测试5失败: Cloze类型应该可编辑"
    print("✓ 测试5通过: 笔记类型限制")
    
    # 测试6: 禁用自动编辑模式
    conf.config["auto_edit_mode"]["enabled"] = False
    result = edit_filter("Hello World", "Front", "", ctx_cloze)
    assert result == "Hello World", f"测试6失败: 禁用模式下不应该可编辑"
    
    # 但显式edit仍然应该工作
    result = edit_filter("Hello World", "Front", "edit", ctx_cloze)
    assert result == expected_cloze, f"测试6失败: 显式edit应该始终工作"
    print("✓ 测试6通过: 禁用自动编辑模式")
    
    print("\n🎉 所有测试通过！自动编辑模式功能正常工作。")

if __name__ == "__main__":
    test_auto_edit_functionality()

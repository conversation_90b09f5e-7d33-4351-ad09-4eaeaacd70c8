{"version": 3, "file": "editor.js", "sources": ["../../../ts/html-filter/helpers.ts", "../../../ts/html-filter/node.ts", "../../../ts/html-filter/styling.ts", "../../../ts/html-filter/element.ts", "../../../ts/html-filter/index.ts", "../../../ts/cross-browser.ts", "../../../ts/wrap.ts", "../../../ts/index.ts"], "sourcesContent": ["// Copyright: Ankitects Pty Ltd and contributors\n// License: GNU AGPL, version 3 or later; http://www.gnu.org/licenses/agpl.html\n\nexport function isHTMLElement(elem: Element): elem is HTMLElement {\n    return elem instanceof HTMLElement;\n}\n\nexport function isNightMode(): boolean {\n    return document.body.classList.contains(\"nightMode\");\n}\n", "// Copyright: Ankitects Pty Ltd and contributors\n// License: GNU AGPL, version 3 or later; http://www.gnu.org/licenses/agpl.html\n\nexport function removeNode(element: Node): void {\n    element.parentNode?.removeChild(element);\n}\n\nfunction iterateElement(\n    filter: (node: Node) => void,\n    fragment: DocumentFragment | Element,\n): void {\n    for (const child of [...fragment.childNodes]) {\n        filter(child);\n    }\n}\n\nexport const filterNode =\n    (elementFilter: (element: Element) => void) =>\n    (node: Node): void => {\n        switch (node.nodeType) {\n            case Node.COMMENT_NODE:\n                removeNode(node);\n                break;\n\n            case Node.DOCUMENT_FRAGMENT_NODE:\n                iterateElement(filterNode(elementFilter), node as DocumentFragment);\n                break;\n\n            case Node.ELEMENT_NODE:\n                iterateElement(filterNode(elementFilter), node as Element);\n                elementFilter(node as Element);\n                break;\n\n            default:\n            // do nothing\n        }\n    };\n", "// Copyright: Ankitects Pty Ltd and contributors\n// License: GNU AGPL, version 3 or later; http://www.gnu.org/licenses/agpl.html\n\ninterface AllowPropertiesBlockValues {\n    [property: string]: string[];\n}\n\ntype BlockProperties = string[];\n\ntype StylingPredicate = (property: string, value: string) => boolean;\n\nconst stylingNightMode: AllowPropertiesBlockValues = {\n    \"font-weight\": [],\n    \"font-style\": [],\n    \"text-decoration-line\": [],\n};\n\nconst stylingLightMode: AllowPropertiesBlockValues = {\n    color: [],\n    \"background-color\": [\"transparent\"],\n    ...stylingNightMode,\n};\n\nconst stylingInternal: BlockProperties = [\n    \"background-color\",\n    \"font-size\",\n    \"font-family\",\n    \"width\",\n    \"height\",\n    \"max-width\",\n    \"max-height\",\n];\n\nconst allowPropertiesBlockValues =\n    (allowBlock: AllowPropertiesBlockValues): StylingPredicate =>\n    (property: string, value: string): boolean =>\n        Object.prototype.hasOwnProperty.call(allowBlock, property) &&\n        !allowBlock[property].includes(value);\n\nconst blockProperties =\n    (block: BlockProperties): StylingPredicate =>\n    (property: string): boolean =>\n        !block.includes(property);\n\nconst filterStyling =\n    (predicate: (property: string, value: string) => boolean) =>\n    (element: HTMLElement): void => {\n        for (const property of [...element.style]) {\n            const value = element.style.getPropertyValue(property);\n\n            if (!predicate(property, value)) {\n                element.style.removeProperty(property);\n            }\n        }\n    };\n\nexport const filterStylingNightMode = filterStyling(\n    allowPropertiesBlockValues(stylingNightMode),\n);\nexport const filterStylingLightMode = filterStyling(\n    allowPropertiesBlockValues(stylingLightMode),\n);\nexport const filterStylingInternal = filterStyling(blockProperties(stylingInternal));\n", "// Copyright: Ankitects Pty Ltd and contributors\n// License: GNU AGPL, version 3 or later; http://www.gnu.org/licenses/agpl.html\n\nimport { isHTMLElement, isNightMode } from \"./helpers\";\nimport { removeNode as removeElement } from \"./node\";\nimport {\n    filterStylingInternal,\n    filterStylingLightMode,\n    filterStylingNightMode,\n} from \"./styling\";\n\ninterface TagsAllowed {\n    [tagName: string]: FilterMethod;\n}\n\ntype FilterMethod = (element: Element) => void;\n\nfunction filterAttributes(\n    attributePredicate: (attributeName: string) => boolean,\n    element: Element,\n): void {\n    for (const attr of [...element.attributes]) {\n        const attrName = attr.name.toUpperCase();\n\n        if (!attributePredicate(attrName)) {\n            element.removeAttributeNode(attr);\n        }\n    }\n}\n\nfunction allowNone(element: Element): void {\n    filterAttributes(() => false, element);\n}\n\nconst allow =\n    (attrs: string[]): FilterMethod =>\n    (element: Element): void =>\n        filterAttributes(\n            (attributeName: string) => attrs.includes(attributeName),\n            element,\n        );\n\nfunction unwrapElement(element: Element): void {\n    element.replaceWith(...element.childNodes);\n}\n\nfunction filterSpan(element: Element): void {\n    const filterAttrs = allow([\"STYLE\"]);\n    filterAttrs(element);\n\n    const filterStyle = isNightMode() ? filterStylingNightMode : filterStylingLightMode;\n    filterStyle(element as HTMLSpanElement);\n}\n\nconst tagsAllowedBasic: TagsAllowed = {\n    BR: allowNone,\n    IMG: allow([\"SRC\", \"ALT\"]),\n    DIV: allowNone,\n    P: allowNone,\n    SUB: allowNone,\n    SUP: allowNone,\n    TITLE: removeElement,\n};\n\nconst tagsAllowedExtended: TagsAllowed = {\n    ...tagsAllowedBasic,\n    A: allow([\"HREF\"]),\n    B: allowNone,\n    BLOCKQUOTE: allowNone,\n    CODE: allowNone,\n    DD: allowNone,\n    DL: allowNone,\n    DT: allowNone,\n    EM: allowNone,\n    FONT: allow([\"COLOR\"]),\n    H1: allowNone,\n    H2: allowNone,\n    H3: allowNone,\n    I: allowNone,\n    LI: allowNone,\n    OL: allowNone,\n    PRE: allowNone,\n    RP: allowNone,\n    RT: allowNone,\n    RUBY: allowNone,\n    SPAN: filterSpan,\n    STRONG: allowNone,\n    TABLE: allowNone,\n    TD: allow([\"COLSPAN\", \"ROWSPAN\"]),\n    TH: allow([\"COLSPAN\", \"ROWSPAN\"]),\n    TR: allow([\"ROWSPAN\"]),\n    U: allowNone,\n    UL: allowNone,\n};\n\nconst filterElementTagsAllowed =\n    (tagsAllowed: TagsAllowed) =>\n    (element: Element): void => {\n        const tagName = element.tagName;\n\n        if (Object.prototype.hasOwnProperty.call(tagsAllowed, tagName)) {\n            tagsAllowed[tagName](element);\n        } else if (element.innerHTML) {\n            unwrapElement(element);\n        } else {\n            removeElement(element);\n        }\n    };\n\nexport const filterElementBasic = filterElementTagsAllowed(tagsAllowedBasic);\nexport const filterElementExtended = filterElementTagsAllowed(tagsAllowedExtended);\n\nexport function filterElementInternal(element: Element): void {\n    if (isHTMLElement(element)) {\n        filterStylingInternal(element);\n    }\n}\n", "// Copyright: Ankitects Pty Ltd and contributors\n// License: GNU AGPL, version 3 or later; http://www.gnu.org/licenses/agpl.html\n\nimport {\n  filterElementBasic,\n  filterElementExtended,\n  filterElementInternal,\n} from \"./element\";\nimport { filterNode } from \"./node\";\n\nenum FilterMode {\n  Basic,\n  Extended,\n  Internal,\n}\n\nconst filters: Record<FilterMode, (element: Element) => void> = {\n  [FilterMode.Basic]: filterElementBasic,\n  [FilterMode.Extended]: filterElementExtended,\n  [FilterMode.Internal]: filterElementInternal,\n};\n\nconst whitespace = /[\\n\\t ]+/g;\n\nfunction collapseWhitespace(value: string): string {\n  return value.replace(whitespace, \" \");\n}\n\nfunction trim(value: string): string {\n  return value.trim();\n}\n\nconst outputHTMLProcessors: Record<FilterMode, (outputHTML: string) => string> =\n  {\n    [FilterMode.Basic]: (outputHTML: string): string =>\n      trim(collapseWhitespace(outputHTML)),\n    [FilterMode.Extended]: trim,\n    [FilterMode.Internal]: trim,\n  };\n\nexport function filterHTML(\n  html: string,\n  internal: boolean,\n  extended: boolean\n): string {\n  const template = document.createElement(\"template\");\n  template.innerHTML = html;\n\n  const mode = getFilterMode(internal, extended);\n  const content = template.content;\n  const filter = filterNode(filters[mode]);\n\n  filter(content);\n\n  return outputHTMLProcessors[mode](template.innerHTML);\n}\n\nfunction getFilterMode(internal: boolean, extended: boolean): FilterMode {\n  if (internal) {\n    return FilterMode.Internal;\n  } else if (extended) {\n    return FilterMode.Extended;\n  } else {\n    return FilterMode.Basic;\n  }\n}\n", "// Copyright: Ankitects Pty Ltd and contributors\n// License: GNU AGPL, version 3 or later; http://www.gnu.org/licenses/agpl.html\n\n/**\n * Gecko has no .getSelection on ShadowRoot, only .activeElement\n */\nexport function getSelection(element: Node): Selection | null {\n  const root = element.getRootNode() as Document;\n\n  if (root.getSelection) {\n    return root.getSelection();\n  }\n\n  return document.getSelection();\n}\n\n/**\n * Browser has potential support for multiple ranges per selection built in,\n * but in reality only Gecko supports it.\n * If there are multiple ranges, the latest range is the _main_ one.\n */\nexport function getRange(selection: Selection): Range | null {\n  const rangeCount = selection.rangeCount;\n\n  return rangeCount === 0 ? null : selection.getRangeAt(rangeCount - 1);\n}\n\n/**\n * Avoid using selection.isCollapsed: it will always return\n * true in shadow root in Gecko\n * (this bug seems to also happens in Blink)\n */\nexport function isSelectionCollapsed(selection: Selection): boolean {\n  return getRange(selection)!.collapsed;\n}\n", "// Copyright: Ankitects Pty Ltd and contributors\n// License: GNU AGPL, version 3 or later; http://www.gnu.org/licenses/agpl.html\n\nimport { getRange, getSelection } from \"./cross-browser\";\n\nfunction wrappedExceptForWhitespace(\n  text: string,\n  front: string,\n  back: string\n): string {\n  const match = text.match(/^(\\s*)([^]*?)(\\s*)$/)!;\n  return match[1] + front + match[2] + back + match[3];\n}\n\nfunction moveCursorInside(selection: Selection, postfix: string): void {\n  const range = getRange(selection)!;\n\n  range.setEnd(range.endContainer, range.endOffset - postfix.length);\n  range.collapse(false);\n\n  selection.removeAllRanges();\n  selection.addRange(range);\n}\n\nexport function wrapInternal(\n  base: Element,\n  front: string,\n  back: string,\n  plainText: boolean\n): void {\n  const selection = getSelection(base)!;\n  const range = getRange(selection);\n\n  if (!range) {\n    return;\n  }\n\n  const wasCollapsed = range.collapsed;\n  const content = range.cloneContents();\n  const span = document.createElement(\"span\");\n  span.appendChild(content);\n\n  if (plainText) {\n    const new_ = wrappedExceptForWhitespace(span.innerText, front, back);\n    document.execCommand(\"inserttext\", false, new_);\n  } else {\n    const new_ = wrappedExceptForWhitespace(span.innerHTML, front, back);\n    document.execCommand(\"inserthtml\", false, new_);\n  }\n\n  if (\n    wasCollapsed &&\n    /* ugly solution: treat <anki-mathjax> differently than other wraps */ !front.includes(\n      \"<anki-mathjax\"\n    )\n  ) {\n    moveCursorInside(selection, back);\n  }\n}\n", "/* Copyright: Ankitects Pty Ltd and contributors\n * License: GNU AGPL, version 3 or later; http://www.gnu.org/licenses/agpl.html */\n\nimport { filterHTML } from \"./html-filter/index\";\nimport { wrapInternal } from \"./wrap\";\n\nexport function setFormat(\n  cmd: string,\n  arg?: any,\n  nosave: boolean = false\n): void {\n  // modified - removed saveField call\n  document.execCommand(cmd, false, arg);\n}\n\ndeclare global {\n  interface Window {\n    EFDRC: any;\n  }\n}\n\nwindow.EFDRC.pasteHTML = function (\n  html: string,\n  internal: boolean,\n  extendedMode: boolean\n): void {\n  html = filterHTML(html, internal, extendedMode);\n\n  if (html !== \"\") {\n    setFormat(\"inserthtml\", html);\n  }\n};\n\nwindow.EFDRC.wrapInternal = wrapInternal;\n"], "names": ["removeElement"], "mappings": ";;;IAAA;IACA;IAEM,SAAU,aAAa,CAAC,IAAa,EAAA;QACvC,OAAO,IAAI,YAAY,WAAW,CAAC;IACvC,CAAC;aAEe,WAAW,GAAA;QACvB,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IACzD;;ICTA;IACA;IAEM,SAAU,UAAU,CAAC,OAAa,EAAA;;QACpC,CAAA,EAAA,GAAA,OAAO,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,WAAW,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;IAED,SAAS,cAAc,CACnB,MAA4B,EAC5B,QAAoC,EAAA;QAEpC,KAAK,MAAM,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,UAAU,CAAC,EAAE;YAC1C,MAAM,CAAC,KAAK,CAAC,CAAC;IACjB,KAAA;IACL,CAAC;IAEM,MAAM,UAAU,GACnB,CAAC,aAAyC,KAC1C,CAAC,IAAU,KAAU;QACjB,QAAQ,IAAI,CAAC,QAAQ;YACjB,KAAK,IAAI,CAAC,YAAY;gBAClB,UAAU,CAAC,IAAI,CAAC,CAAC;gBACjB,MAAM;YAEV,KAAK,IAAI,CAAC,sBAAsB;gBAC5B,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,IAAwB,CAAC,CAAC;gBACpE,MAAM;YAEV,KAAK,IAAI,CAAC,YAAY;gBAClB,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,IAAe,CAAC,CAAC;gBAC3D,aAAa,CAAC,IAAe,CAAC,CAAC;gBAC/B,MAAM;;IAIb,KAAA;IACL,CAAC;;ICpCL;IACA;IAUA,MAAM,gBAAgB,GAA+B;IACjD,IAAA,aAAa,EAAE,EAAE;IACjB,IAAA,YAAY,EAAE,EAAE;IAChB,IAAA,sBAAsB,EAAE,EAAE;KAC7B,CAAC;IAEF,MAAM,gBAAgB,GAA+B;IACjD,IAAA,KAAK,EAAE,EAAE;QACT,kBAAkB,EAAE,CAAC,aAAa,CAAC;IACnC,IAAA,GAAG,gBAAgB;KACtB,CAAC;IAEF,MAAM,eAAe,GAAoB;QACrC,kBAAkB;QAClB,WAAW;QACX,aAAa;QACb,OAAO;QACP,QAAQ;QACR,WAAW;QACX,YAAY;KACf,CAAC;IAEF,MAAM,0BAA0B,GAC5B,CAAC,UAAsC,KACvC,CAAC,QAAgB,EAAE,KAAa,KAC5B,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC;QAC1D,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAE9C,MAAM,eAAe,GACjB,CAAC,KAAsB,KACvB,CAAC,QAAgB,KACb,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAElC,MAAM,aAAa,GACf,CAAC,SAAuD,KACxD,CAAC,OAAoB,KAAU;QAC3B,KAAK,MAAM,QAAQ,IAAI,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,EAAE;YACvC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAEvD,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE;IAC7B,YAAA,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IAC1C,SAAA;IACJ,KAAA;IACL,CAAC,CAAC;IAEC,MAAM,sBAAsB,GAAG,aAAa,CAC/C,0BAA0B,CAAC,gBAAgB,CAAC,CAC/C,CAAC;IACK,MAAM,sBAAsB,GAAG,aAAa,CAC/C,0BAA0B,CAAC,gBAAgB,CAAC,CAC/C,CAAC;IACK,MAAM,qBAAqB,GAAG,aAAa,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;;IC9DpF;IAiBA,SAAS,gBAAgB,CACrB,kBAAsD,EACtD,OAAgB,EAAA;QAEhB,KAAK,MAAM,IAAI,IAAI,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,EAAE;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;IAEzC,QAAA,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE;IAC/B,YAAA,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IACrC,SAAA;IACJ,KAAA;IACL,CAAC;IAED,SAAS,SAAS,CAAC,OAAgB,EAAA;QAC/B,gBAAgB,CAAC,MAAM,KAAK,EAAE,OAAO,CAAC,CAAC;IAC3C,CAAC;IAED,MAAM,KAAK,GACP,CAAC,KAAe,KAChB,CAAC,OAAgB,KACb,gBAAgB,CACZ,CAAC,aAAqB,KAAK,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,EACxD,OAAO,CACV,CAAC;IAEV,SAAS,aAAa,CAAC,OAAgB,EAAA;QACnC,OAAO,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;IAC/C,CAAC;IAED,SAAS,UAAU,CAAC,OAAgB,EAAA;QAChC,MAAM,WAAW,GAAG,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QACrC,WAAW,CAAC,OAAO,CAAC,CAAC;IAErB,IAAA,MAAM,WAAW,GAAG,WAAW,EAAE,GAAG,sBAAsB,GAAG,sBAAsB,CAAC;QACpF,WAAW,CAAC,OAA0B,CAAC,CAAC;IAC5C,CAAC;IAED,MAAM,gBAAgB,GAAgB;IAClC,IAAA,EAAE,EAAE,SAAS;QACb,GAAG,EAAE,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC1B,IAAA,GAAG,EAAE,SAAS;IACd,IAAA,CAAC,EAAE,SAAS;IACZ,IAAA,GAAG,EAAE,SAAS;IACd,IAAA,GAAG,EAAE,SAAS;IACd,IAAA,KAAK,EAAEA,UAAa;KACvB,CAAC;IAEF,MAAM,mBAAmB,GAAgB;IACrC,IAAA,GAAG,gBAAgB;IACnB,IAAA,CAAC,EAAE,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;IAClB,IAAA,CAAC,EAAE,SAAS;IACZ,IAAA,UAAU,EAAE,SAAS;IACrB,IAAA,IAAI,EAAE,SAAS;IACf,IAAA,EAAE,EAAE,SAAS;IACb,IAAA,EAAE,EAAE,SAAS;IACb,IAAA,EAAE,EAAE,SAAS;IACb,IAAA,EAAE,EAAE,SAAS;IACb,IAAA,IAAI,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC;IACtB,IAAA,EAAE,EAAE,SAAS;IACb,IAAA,EAAE,EAAE,SAAS;IACb,IAAA,EAAE,EAAE,SAAS;IACb,IAAA,CAAC,EAAE,SAAS;IACZ,IAAA,EAAE,EAAE,SAAS;IACb,IAAA,EAAE,EAAE,SAAS;IACb,IAAA,GAAG,EAAE,SAAS;IACd,IAAA,EAAE,EAAE,SAAS;IACb,IAAA,EAAE,EAAE,SAAS;IACb,IAAA,IAAI,EAAE,SAAS;IACf,IAAA,IAAI,EAAE,UAAU;IAChB,IAAA,MAAM,EAAE,SAAS;IACjB,IAAA,KAAK,EAAE,SAAS;QAChB,EAAE,EAAE,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACjC,EAAE,EAAE,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IACjC,IAAA,EAAE,EAAE,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC;IACtB,IAAA,CAAC,EAAE,SAAS;IACZ,IAAA,EAAE,EAAE,SAAS;KAChB,CAAC;IAEF,MAAM,wBAAwB,GAC1B,CAAC,WAAwB,KACzB,CAAC,OAAgB,KAAU;IACvB,IAAA,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;IAEhC,IAAA,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE;IAC5D,QAAA,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC;IACjC,KAAA;aAAM,IAAI,OAAO,CAAC,SAAS,EAAE;YAC1B,aAAa,CAAC,OAAO,CAAC,CAAC;IAC1B,KAAA;IAAM,SAAA;YACHA,UAAa,CAAC,OAAO,CAAC,CAAC;IAC1B,KAAA;IACL,CAAC,CAAC;IAEC,MAAM,kBAAkB,GAAG,wBAAwB,CAAC,gBAAgB,CAAC,CAAC;IACtE,MAAM,qBAAqB,GAAG,wBAAwB,CAAC,mBAAmB,CAAC,CAAC;IAE7E,SAAU,qBAAqB,CAAC,OAAgB,EAAA;IAClD,IAAA,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;YACxB,qBAAqB,CAAC,OAAO,CAAC,CAAC;IAClC,KAAA;IACL;;ICpHA;IAUA,IAAK,UAIJ,CAAA;IAJD,CAAA,UAAK,UAAU,EAAA;IACb,IAAA,UAAA,CAAA,UAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;IACL,IAAA,UAAA,CAAA,UAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ,CAAA;IACR,IAAA,UAAA,CAAA,UAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ,CAAA;IACV,CAAC,EAJI,UAAU,KAAV,UAAU,GAId,EAAA,CAAA,CAAA,CAAA;IAED,MAAM,OAAO,GAAmD;IAC9D,IAAA,CAAC,UAAU,CAAC,KAAK,GAAG,kBAAkB;IACtC,IAAA,CAAC,UAAU,CAAC,QAAQ,GAAG,qBAAqB;IAC5C,IAAA,CAAC,UAAU,CAAC,QAAQ,GAAG,qBAAqB;KAC7C,CAAC;IAEF,MAAM,UAAU,GAAG,WAAW,CAAC;IAE/B,SAAS,kBAAkB,CAAC,KAAa,EAAA;QACvC,OAAO,KAAK,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACxC,CAAC;IAED,SAAS,IAAI,CAAC,KAAa,EAAA;IACzB,IAAA,OAAO,KAAK,CAAC,IAAI,EAAE,CAAC;IACtB,CAAC;IAED,MAAM,oBAAoB,GACxB;IACE,IAAA,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,UAAkB,KACrC,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;IACtC,IAAA,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI;IAC3B,IAAA,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI;KAC5B,CAAC;aAEY,UAAU,CACxB,IAAY,EACZ,QAAiB,EACjB,QAAiB,EAAA;QAEjB,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IACpD,IAAA,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;QAE1B,MAAM,IAAI,GAAG,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC/C,IAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;QACjC,MAAM,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QAEzC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEhB,OAAO,oBAAoB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IACxD,CAAC;IAED,SAAS,aAAa,CAAC,QAAiB,EAAE,QAAiB,EAAA;IACzD,IAAA,IAAI,QAAQ,EAAE;YACZ,OAAO,UAAU,CAAC,QAAQ,CAAC;IAC5B,KAAA;IAAM,SAAA,IAAI,QAAQ,EAAE;YACnB,OAAO,UAAU,CAAC,QAAQ,CAAC;IAC5B,KAAA;IAAM,SAAA;YACL,OAAO,UAAU,CAAC,KAAK,CAAC;IACzB,KAAA;IACH;;ICjEA;IACA;IAEA;;IAEG;IACG,SAAU,YAAY,CAAC,OAAa,EAAA;IACxC,IAAA,MAAM,IAAI,GAAG,OAAO,CAAC,WAAW,EAAc,CAAC;QAE/C,IAAI,IAAI,CAAC,YAAY,EAAE;IACrB,QAAA,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;IAC5B,KAAA;IAED,IAAA,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC;IACjC,CAAC;IAED;;;;IAIG;IACG,SAAU,QAAQ,CAAC,SAAoB,EAAA;IAC3C,IAAA,MAAM,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;IAExC,IAAA,OAAO,UAAU,KAAK,CAAC,GAAG,IAAI,GAAG,SAAS,CAAC,UAAU,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;IACxE;;ICzBA;IAKA,SAAS,0BAA0B,CACjC,IAAY,EACZ,KAAa,EACb,IAAY,EAAA;QAEZ,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAE,CAAC;IACjD,IAAA,OAAO,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC;IAED,SAAS,gBAAgB,CAAC,SAAoB,EAAE,OAAe,EAAA;IAC7D,IAAA,MAAM,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAE,CAAC;IAEnC,IAAA,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IACnE,IAAA,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAEtB,SAAS,CAAC,eAAe,EAAE,CAAC;IAC5B,IAAA,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAEK,SAAU,YAAY,CAC1B,IAAa,EACb,KAAa,EACb,IAAY,EACZ,SAAkB,EAAA;IAElB,IAAA,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAE,CAAC;IACtC,IAAA,MAAM,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;QAElC,IAAI,CAAC,KAAK,EAAE;YACV,OAAO;IACR,KAAA;IAED,IAAA,MAAM,YAAY,GAAG,KAAK,CAAC,SAAS,CAAC;IACrC,IAAA,MAAM,OAAO,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;QACtC,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IAC5C,IAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAE1B,IAAA,IAAI,SAAS,EAAE;IACb,QAAA,MAAM,IAAI,GAAG,0BAA0B,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YACrE,QAAQ,CAAC,WAAW,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IACjD,KAAA;IAAM,SAAA;IACL,QAAA,MAAM,IAAI,GAAG,0BAA0B,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YACrE,QAAQ,CAAC,WAAW,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;IACjD,KAAA;IAED,IAAA,IACE,YAAY;mFAC2D,CAAC,KAAK,CAAC,QAAQ,CACpF,eAAe,CAChB,EACD;IACA,QAAA,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IACnC,KAAA;IACH;;IC1DA;IACkF;IAK5E,SAAU,SAAS,CACvB,GAAW,EACX,GAAS,EACT,SAAkB,KAAK,EAAA;;QAGvB,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;IACxC,CAAC;IAQD,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG,UACvB,IAAY,EACZ,QAAiB,EACjB,YAAqB,EAAA;QAErB,IAAI,GAAG,UAAU,CAAC,IAAI,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;QAEhD,IAAI,IAAI,KAAK,EAAE,EAAE;IACf,QAAA,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IAC/B,KAAA;IACH,CAAC,CAAC;IAEF,MAAM,CAAC,KAAK,CAAC,YAAY,GAAG,YAAY;;;;;;;;;;;;"}
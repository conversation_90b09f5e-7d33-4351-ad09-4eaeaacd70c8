[data-efdrcfield][contenteditable="true"].EFDRC-outline:focus {
  outline: 1px solid #308cc6;
}
/* placeholder style */
[data-efdrcfield][contenteditable="true"][data-placeholder].EFDRC-ctrl:empty:before {
  content: attr(data-placeholder);
  color: #888;
  font-style: italic;
}

/* 双击和划词编辑的视觉提示 */
[data-efdrcfield][contenteditable="false"] {
  cursor: text;
  position: relative;
}

[data-efdrcfield][contenteditable="false"]:hover {
  background-color: rgba(48, 140, 198, 0.1);
  border-radius: 2px;
}

/* 选中文本时的样式 */
[data-efdrcfield]::selection {
  background-color: rgba(48, 140, 198, 0.3);
}

[data-efdrcfield]::-moz-selection {
  background-color: rgba(48, 140, 198, 0.3);
}

/* image resizing */
.ui-wrapper {
  outline: 3px solid #66b3da;
  overflow: visible !important;
}
.ui-resizable {
  position: relative;
}
.ui-resizable-handle {
  position: absolute;
  font-size: 0.1px;
  display: block;
  -ms-touch-action: none;
  touch-action: none;
}
.ui-resizable-disabled .ui-resizable-handle,
.ui-resizable-autohide .ui-resizable-handle {
  display: none;
}
.ui-resizable-s {
  cursor: s-resize;
  height: 10px;
  width: auto;
  bottom: -5px;
  left: 0;
  right: 6px;
}
.ui-resizable-e {
  cursor: e-resize;
  width: 10px;
  height: auto;
  right: -5px;
  top: 0;
  bottom: 6px;
}
.ui-resizable-se {
  background: #66b3da;
  cursor: se-resize;
  width: 11px;
  height: 11px;
  right: -7px;
  bottom: -7px;
}
